# 自动生成的工具链文件 - Fri Sep 19 17:22:58 HKT 2025
message(STATUS "Loading arm toolchain from arm-toolchain.cmake - Version: 1.01.002")

# 设置目标系统名称
SET(CMAKE_SYSTEM_NAME Linux)

# 设置目标处理器架构
SET(CMAKE_SYSTEM_PROCESSOR arm)

# 指定交叉编译器的路径
SET(CMAKE_C_COMPILER /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-gcc)
SET(CMAKE_CXX_COMPILER /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-g++)

# 指定 strip 工具路径
SET(CMAKE_STRIP /home/<USER>/ti-processor-sdk-linux-rt-am62xx-evm-***********/external-toolchain-dir/arm-gnu-toolchain-11.3.rel1-x86_64-arm-none-linux-gnueabihf/bin/arm-none-linux-gnueabihf-strip)
