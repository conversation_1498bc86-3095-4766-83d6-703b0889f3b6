
#ifndef __MLPFILE_H__
#define __MLPFILE_H__

/**
 * @file file header
 */

/*--------------------------------------------------------------------
                            INCLUDES
--------------------------------------------------------------------*/
#include <stdint.h>
#include "utl_geo.h"
//#include "rect.h"
#include "gfx_pub.h"


#include <stdlib.h>
#include <stdio.h>
#include <string.h>



#include "mlp_tree.h"
//#include "dbase.h"



#define map_malloc malloc
#define map_free   free


#define CONFIGE_ONE_BUF  1


/*--------------------------------------------------------------------
                          <PERSON><PERSON><PERSON>AL CONSTANTS
--------------------------------------------------------------------*/
#define SCALE_MIN_VAL   SCALE_1_20M
#define SCALE_MAX_VAL   SCALE_1_100KM

#define BKGD_COLOR          GFX_RGB(200, 200, 210)//背景色
#define PEN_COLOR           GFX_RGB(255, 216, 107)
#define HIGHWAY_COLOR       GFX_RGB(255, 182, 118)
#define OTHER_ROAD_COLOR    GFX_RGB(255, 255, 255)

//#define DATABASE_PATH "storage/sd0/C/"
#ifdef CONFIG_MASS_STORAGE_DISK_NAME
#define DATABASE_PATH "/" CONFIG_MASS_STORAGE_DISK_NAME ":/"
#else
#define DATABASE_PATH "/SD:/"
#endif


#ifdef CONFIG_MASS_STORAGE_DISK_NAME
#define INPUTREC_DEFAULT_FILE_PATH_1 "/" CONFIG_MASS_STORAGE_DISK_NAME ":/.input.rec"
#else
#define INPUTREC_DEFAULT_FILE_PATH_1 "/SD:/.input.rec"
#endif


// building colors
#define RESIDENTIAL_COLOR   GFX_RGB(225, 235, 242)
/*--------------------------------------------------------------------
                               TYPES
--------------------------------------------------------------------*/
// 比例尺
enum {
    SCALE_1_10000KM = 0,    // 1:10000KM
    SCALE_1_5000KM  = 1,    // 1:5000KM
    SCALE_1_2000KM  = 2,    // 1:2000KM
    SCALE_1_1000KM  = 3,    // 1:1000KM
    SCALE_1_500KM   = 4,    // 1:500KM
    SCALE_1_200KM   = 5,    // 1:200KM
    SCALE_1_100KM   = 6,    // 1:100KM
    SCALE_1_50KM    = 7,    // 1:50KM
    SCALE_1_25KM    = 8,    // 1:25KM
    SCALE_1_20KM    = 9,    // 1:20KM
    SCALE_1_10KM    = 10,   // 1:10KM
    SCALE_1_5KM     = 11,   // 1:5KM
    SCALE_1_2KM     = 12,   // 1:2KM
    SCALE_1_1KM     = 13,   // 1:1KM
    SCALE_1_500M    = 14,   // 1:500M
    SCALE_1_200M    = 15,   // 1:200M
    SCALE_1_100M    = 16,   // 1:100M
    SCALE_1_50M     = 17,   // 1:50M
    SCALE_1_20M     = 18,   // 1:20M
};



struct rect {
    int left;
    int top;
    int width;
    int height;
};


typedef struct {
    int32_t lat;
    int32_t lon;
} trk_pos_t;


//typedef struct  mlp_map_module {
//    struct fs_file_t mlp_fh;           // mlp file handle
//    struct fs_file_t idx_fh;           // index file handle
//    int mlp_type;         // mlp type
//    struct db_hndl_t* db_hndl;
//
//    int (* filter_callback)(struct mlp_map_module *pmodule, int code);      // 根据code和缩放等级进行过滤
//    int (* color_callback)(struct mlp_map_module *pmodule, int code);       // 根据code进行颜色设置
//} mlp_map_module_t;

//typedef  void (*mlp_fill_polygon)(gfx_point_t* points, uint16_t num, bool border); 
//typedef  void (*mlp_draw_line)(int32_t x0, int32_t y0, int32_t x1, int32_t y1, gfx_color_t color, uint16_t width);  


//typedef struct  mlp_map_draw {
//
//    void (*fill_polygon)(gfx_point_t* points, uint16_t num, bool border);      
//    void (*draw_line)(int32_t x0, int32_t y0, int32_t x1, int32_t y1, gfx_color_t color, uint16_t width);     
//} mlp_map_draw_t;


typedef bool(*mlp_get_coord_fun_t)(double* lon, double* lat);

//3种展示模式
typedef uint8_t show_mode_t; 
enum {
    ROUTINE_MODE = 0x00,   	// 地貌模式
    CONTOUR_MODE = 0x01,   	// 等高线模式
    MIX_MODE = 0x02,   		// 混合模式
};

//5种地图功能模式
typedef uint8_t page_mode_t;
enum
{
	POS_MODE = 0x01, // 地图模式
	NAV_MODE = 0x02, // 导航模式
	REC_MODE = 0x04, // 记录模式
	RET_MODE = 0x08, // 返航模式
	VIW_MODE = 0x80, // 预览模式
};

//6种地图绘制状态
typedef uint8_t draw_status_t;
enum
{
	IDLE_STATUS,
	MAP_STATUS,
	PARSE_STATUS,
	TRK_STATUS,
	FLUSH_STATUS,
	FINISH_STATUS,
};



/*******************************************
*		MAP UI层结构体
*******************************************/

////记录下打开的等高线文件
//typedef struct gps_contour_file_t{
//	struct fs_file_t mlp_fh;           // mlp file handle
//    struct fs_file_t idx_fh;           // index file handle
//    struct db_hndl_t* db_hndl;
//}gps_contour_file_t;



typedef struct gps_map_ratio_t{
	double ratio;								//暂未用到
	uint8_t* buff;							//画布缓冲区
	uint8_t scale;								//比例尺
	uint8_t done;								//画布是否绘制完成
	uint8_t show;								//暂未用到
	uint8_t index;								//暂未用到
	uint8_t cur_point_show; 					//是否展示了当前点
	utl_geo_coord_t m_coord_draw;				//当前画布绘制的中心点
}gps_map_ratio_t;

#define MPS_MAP_BUFF_MAX_NUM 3

typedef struct gps_map_t{
	uint8_t is_updata_done; 									//一轮绘画是否结束
	 										
	uint8_t draw_index; 										//当前绘画的哪个画布
	uint8_t draw_scale; 										//当前绘画的比例尺
	uint8_t is_need_calculate;									//暂未用到
	uint8_t show_index;											//当前展示的画布
	uint8_t last_show_index;									//暂未用到
	uint8_t show_scale; 										//当前展示的比例尺
	uint8_t scale_array_done[6];								//6个比例尺的绘制状态：1-绘制完 0-没有绘制
	uint8_t mult_res_total; 									//地图数量
	uint8_t mult_res_active;									//打开的哪个地图
	uint8_t zoom;												//放大或缩小：2-放大 1-缩小

	uint8_t map_show_mode;										//地图展示模式：地貌模式、等高线模式、混合模式

	
	struct gps_map_ratio_t map_ratio[MPS_MAP_BUFF_MAX_NUM];
}gps_map_t;

typedef struct touch_point_t{
    uint32_t x;
    uint32_t y;
}touch_point_t;

/*--------------------------------------------------------------------
                          MEMORY CONSTANTS
--------------------------------------------------------------------*/

/*--------------------------------------------------------------------
                              MACROS
--------------------------------------------------------------------*/
//#define PIXELSPERCM     100

 extern int pixelspercm;

// 大端转小端
#define be32tole32(x)     ((((x) >> 24) & 0xff) | (((x) << 8) & 0xff0000) | \
                                (((x) >> 8) & 0xff00) | (((x) << 24) & 0xff000000))

#define DIST2RATIO(x)   ((double)(x) / pixelspercm)

typedef int(* color_callback_t)(struct mlp_map_module *pmodule, int code);

/*--------------------------------------------------------------------
                            PROCEDURES
--------------------------------------------------------------------*/
/**
 * 画地图
 */
//void mlp_draw_map(void *_dc, struct rect rect, utl_geo_point_t pos, utl_geo_coord_t coord, int scale);

int mlp_map_init(color_callback_t road_callback,color_callback_t landuse_callback, color_callback_t water_callback,color_callback_t contour_callback,uint8_t * code);


void mlp_map_uninit(void);

//void mlp_read_shx(mlp_map_module_t* pmodule, uint32_t* precord, uint32_t code, uint32_t record_len);

void mlp_move_rect(int delta_x, int delta_y, utl_geo_coord_t* coord);

//void mlp_draw_track(mlp_get_coord_fun_t coord_callback, int color, uint16_t width);

// 1 中断地图引擎加载;  0 中断地图引擎加载 ，默认不中断
void set_mlp_int_flag(uint8_t flag);

//获取中断地图引擎加载标志 
uint8_t get_mlp_int_flag(void);

// 1 过滤土地使用， 0不过滤;默认为1 过滤
void set_landuse_filter_flag(uint8_t flag);


uint8_t get_landuse_filter_flag(void);

/*
打开地图文件
roads_mlp_path: roads.mlp文件路径
roads_idx_path: roads.idx文件路径
landuse_mlp_path: landuse.mlp文件路径
landuse_idx_path: landuse.mlp文件路径
water_mlp_path: water.mlp文件路径
water_idx_path: water.mlp文件路径

*/
void mlp_map_open_file(const char *roads_mlp_path,const char *roads_idx_path,const char *landuse_mlp_path,const char *landuse_idx_path,const char *water_mlp_path,const char *water_idx_path);

int road_color_callback(struct mlp_map_module* pmodule, int code);
int water_color_callback(struct mlp_map_module* pmodule, int code);
int landuse_color_callback(struct mlp_map_module* pmodule, int code);

/**
 * @brief 根据经纬度，获取该点在屏幕上坐标
 * @param[out] coord 		经纬度
 * @param[out] pos 			坐标位置
 */
void map_coord_get_pos(utl_geo_coord_t coord, gfx_point_t *pos);


/**
 * @brief 根据X轴Y轴的位移，计算现在偏移后的坐标
 * @param[out] delta_x 			x轴向的位移，如按下屏幕时的x，离开屏幕时的x，两者差值
 * @param[out] delta_y 			y轴向的位移，如按下屏幕时的y，离开屏幕时的y，两者差值
 * @param[out] coord 			地图中心点坐标
 */
void mlp_move_rect(int delta_x, int delta_y, utl_geo_coord_t* coord);




/**
 * @brief 根据经纬度计算出比例尺及中心
 * @param[out] min_lon 			最小的经度
 * @param[out] min_lat 			最小的纬度
 * @param[out] max_lon 			最大的经度
 * @param[out] max_lat 			最大的纬度
 * @param[out] resolution 		较大分辨率
 */
int mlp_calc_scale(double min_lon, double min_lat, double max_lon, double max_lat, uint16_t resolution);



/**
 * @brief 根据当前等高线文件的打开状态，控制剩下的等高线打开与回调
 * @param[out] open_status 		文件打开状态
 * @param[out] index 			当前绘制的画布
 * @param[out] buf 				当前绘制的缓冲区
 * @param[out] coord 			当前绘制的范围
 */
void control_draw_contour(int open_status,int index,tree_rect_t coord);

/*********************************************************************************
 * 
 * @brief 判断当前画布的等高线绘画状态
 *
 * @param[out]: coord1_max - 画布的最大经纬度		
 * @param[out]: coord1_mim - 画布的最小经纬度
 * @param[out]: coord2_max - 等高线的最大经纬度	
 * @param[out]: coord2_mim - 等高线的最小经纬度
 *
 * 返回值：0-绘制正常 1-画布在右上角 2-画布在左上角	3-画布在右下角 4-画布在左下角
 * 			5-画布压右边 6-画布压左边 7-画布压上边 8-画布压下边	9-画布不属于这个经纬度
 * 
 * *****************************************************************************/
int contour_contain_func(utl_geo_coord_t coord1_max,utl_geo_coord_t coord1_min,utl_geo_coord_t coord2_max,utl_geo_coord_t coord2_min);


//路网回调
int loads_callback(tree_rect_t* R);
//土地使用回调
int landuse_callback(tree_rect_t* R);
//水域回调
int water_callback(tree_rect_t* R);
//等高线回调
int contour_callback(tree_rect_t* R);
//等高线颜色回调
int contour_color_callback(struct mlp_map_module* pmodule, int code);
//获取打开的地图文件个数
int get_map_open_file_nums();



//计算比例尺
void mlp_ratio_cal(utl_geo_coord_t coord_cur,int scale);

//水域 线宽、颜色
void set_water_color(int line_size ,int color);

//高速路线宽、颜色
void set_motorway_roads_color(int line_size ,int color);


//主干道 线宽、颜色
void set_trunk_roads_color(int line_size ,int color);


//干道 线宽、颜色
void set_primary_roads_color(int line_size ,int color);

//其他道路线宽、颜色
void set_other_roads_color(int line_size ,int color);


//商业土地 线宽、颜色
void set_commercial_landuse_color(int line_size ,int color);

//荒地 线宽、颜色
void set_heath_landuse_color(int line_size ,int color);

//公园/林地 线宽、颜色
void set_park_landuse_color(int line_size ,int color);

//其他区域 线宽、颜色
void set_other_landuse_color(int line_size ,int color);

//判断坐标点是否在文件中
int location_is_in_mapfile( struct rect rect,utl_geo_coord_t coord_cur);

int location_is_in_mapfile_new(struct rect rect,utl_geo_coord_t coord_cur,file_t m_idx);

//遍历MAP文件夹
int search_map_dir(char* path);

//打开多文件地图资源
int mlp_map_open_multi_file(char* file_name,utl_geo_coord_t coord);

//获取当前正在绘制的等高线
int get_cur_contour(void);

//等高线的释放
void contour_uinit();

//检查当前点是否在打开的区域文件中 1 - 在   0 - 不在
int check_cur_is_in_mapfile(utl_geo_coord_t coord);

//关闭地图文件
void map_close_files();

//根据坐标点获取经纬度
void map_pos_get_coord(touch_point_t pos, utl_geo_coord_t *coord);


//计算2个经纬度之间的距离
int GpsDistance_self(utl_geo_coord_t lot_lat1, utl_geo_coord_t lot_lat2);




/*********************************************************************
*
*  功能描述
*    PC授权工具点击【授权】后，腕表端返回其MAC地址！！
*
*  输入参数
*    recv：从PC串口发来的数据
*    void(*p)(void *, uint16_t),回调串口发送函数，按字节发送
*
**********************************************************************/
void Map_reply_id_addr(unsigned char* recv,void(*p)(void *, uint16_t));


/*********************************************************************
*
*  功能描述
*    查看当前LisenceId是否有效
*
*  参数
*    code：64个字节的code，如果验证正确才可使用该算法
*	 void(*p)(void *, uint16_t),回调串口发送函数，按字节发送
*
*  返回值
*		 返回校验状态：true（1）表示成功 false（0）表示失败
*
**********************************************************************/
unsigned char Map_check_lisenceId_status(uint8_t * code,void(*p)(void *, uint16_t));



/************************************************************************************************
			����CRCУ�麯��
*************************************************************************************************/

unsigned char MAP_CRC(unsigned char* data) ;


void set_wdth_hght(int wdth,int hght);

void get_wdth_hght(int* wdth,int* hght);

void set_pixelspercm(int i);

int get_pixelspercm(void);


#endif /* __MLPFILE_H__ */
